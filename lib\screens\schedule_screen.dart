import 'package:flutter/material.dart';
import '../models/course.dart';
import '../utils/colors.dart';

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  int selectedDay = 20; // Thursday, May 20, 2025
  
  // Sample lecture data
  late final List<Lecture> lectures;

  @override
  void initState() {
    super.initState();
    _initializeLectures();
  }

  void _initializeLectures() {
    lectures = [
      Lecture(
        id: '1',
        title: 'Database system',
        instructor: 'Dr. <PERSON>',
        startTime: DateTime(2025, 5, 20, 9, 0),
        endTime: DateTime(2025, 5, 20, 10, 30),
        hasNotification: true,
        category: 'Computer Science',
      ),
      Lecture(
        id: '2',
        title: 'UI UX',
        instructor: 'Dr. <PERSON>',
        startTime: DateTime(2025, 5, 20, 2, 0),
        endTime: DateTime(2025, 5, 20, 4, 30),
        hasNotification: false,
        category: 'Design',
      ),
      Lecture(
        id: '3',
        title: 'Database system',
        instructor: 'Dr. <PERSON>',
        startTime: DateTime(2025, 5, 20, 6, 0),
        endTime: DateTime(2025, 5, 20, 8, 30),
        hasNotification: false,
        category: 'Computer Science',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Calendar Section
            _buildCalendarSection(),
            
            // Upcoming Lectures Section
            Expanded(
              child: _buildUpcomingLectures(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.cardBackground,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.borderColor),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                size: 18,
                color: AppColors.primaryText,
              ),
            ),
          ),
          const Expanded(
            child: Text(
              'Schedule',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryText,
              ),
            ),
          ),
          const SizedBox(width: 40), // Balance the back button
        ],
      ),
    );
  }

  Widget _buildCalendarSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Column(
        children: [
          // Month and Year
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'May , 2025',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppColors.primaryText.withValues(alpha: 0.6),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Week days
          _buildWeekDays(),
        ],
      ),
    );
  }

  Widget _buildWeekDays() {
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final dates = [17, 18, 19, 20, 21, 22, 23];
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: List.generate(7, (index) {
        final isSelected = dates[index] == selectedDay;
        final isToday = dates[index] == 20; // Thursday is today
        
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedDay = dates[index];
            });
          },
          child: Column(
            children: [
              Text(
                days[index],
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.secondaryText,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primaryBlue : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    '${dates[index]}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? Colors.white : AppColors.primaryText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildUpcomingLectures() {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Upcoming Lectures',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryText,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: lectures.length,
              itemBuilder: (context, index) {
                return _buildLectureCard(lectures[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLectureCard(Lecture lecture) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          // Time
          Column(
            children: [
              Text(
                lecture.timeDisplay.split(' ')[0],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                ),
              ),
              Text(
                lecture.timeDisplay.split(' ')[1],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                ),
              ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // Play button
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Lecture details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  lecture.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryText,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: 14,
                      color: AppColors.secondaryText,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      lecture.instructor,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: AppColors.secondaryText,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      lecture.formattedTimeRange,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Notification icon
          if (lecture.hasNotification)
            Icon(
              Icons.notifications,
              color: AppColors.primaryBlue,
              size: 20,
            ),
        ],
      ),
    );
  }
}
